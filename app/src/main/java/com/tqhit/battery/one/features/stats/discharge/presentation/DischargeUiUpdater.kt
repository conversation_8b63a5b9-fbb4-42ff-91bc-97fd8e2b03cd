package com.tqhit.battery.one.features.stats.discharge.presentation

import android.content.Context
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.View
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.NewFragmentDischargeBinding
import com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter

/**
 * Handles UI updates for the discharge screen
 */
class DischargeUiUpdater(
    private val context: Context,
    private val binding: NewFragmentDischargeBinding,
    private val timeConverter: TimeConverter
) {
    companion object {
        private const val TAG = "DischargeUiUpdater"
    }

    /**
     * Updates the status and estimates section
     */
    fun updateStatusAndEstimates(state: DischargeUiState) {
        if (state.isLoadingInitial) {
            // Display loading placeholders
            binding.includeStatusAndEstimates.saeTvPercentage.text = "..."
            binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = context.getString(R.string.charging, "...")
            Log.d(TAG, "Showing loading placeholders")
        } else {
            // Set the battery percentage directly (animation is handled separately)
            binding.includeStatusAndEstimates.saeTvPercentage.text = "${state.batteryPercentage}%"
            setColoredChargingText(state.batteryPercentage, state.isCharging)

            // Update time estimates
            binding.includeStatusAndEstimates.saeTvScreenOnTime.text =
                timeConverter.formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)
            binding.includeStatusAndEstimates.saeTvMixedUsageTime.text =
                timeConverter.formatMillisToHoursMinutes(state.mixedUsageTimeRemainingMs)
            binding.includeStatusAndEstimates.saeTvScreenOffTime.text =
                timeConverter.formatMillisToHoursMinutes(state.screenOffTimeRemainingMs)

            Log.d(TAG, "Updated time estimations display - " +
                 "Screen ON: ${timeConverter.formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)}, " +
                 "Mixed: ${timeConverter.formatMillisToHoursMinutes(state.mixedUsageTimeRemainingMs)}, " +
                 "Screen OFF: ${timeConverter.formatMillisToHoursMinutes(state.screenOffTimeRemainingMs)}")
        }
        
        // Show or hide the charging message based on charging state
        if (state.isCharging) {
            binding.dischargeChargingMessage.visibility = View.VISIBLE
            Log.d(TAG, "Device is charging. Showing charging message and hiding detailed sections.")
        } else {
            binding.dischargeChargingMessage.visibility = View.GONE
        }
    }

    /**
     * Updates the loss of charge section
     */
    fun updateLossOfCharge(state: DischargeUiState) {
        val session = state.currentSession
        if (session == null || state.isLoadingInitial || state.isCharging) {
            // Hide section or show placeholders if no active session
            binding.includeLossOfCharge.root.visibility = View.GONE
            return
        }

        binding.includeLossOfCharge.root.visibility = View.VISIBLE

        // Use real-time consumption values from UI state for more accurate display
        val screenOnPercentDropped = state.screenOnConsumePercentage
        val screenOffPercentDropped = state.screenOffConsumePercentage

        // Calculate mAh consumed from percentage values using battery capacity
        val batteryCapacityMah = state.batteryCapacityMah.toDouble()
        val screenOnMahConsumed = (screenOnPercentDropped / 100.0) * batteryCapacityMah
        val screenOffMahConsumed = (screenOffPercentDropped / 100.0) * batteryCapacityMah

        // Screen on consumption - display real-time values
        binding.includeLossOfCharge.locTvScreenOnPercentageDropped.text =
            String.format("%.1f", screenOnPercentDropped)
        binding.includeLossOfCharge.locTvScreenOnMahConsumed.text =
            String.format("%.1f", screenOnMahConsumed)

        // Screen off consumption - display real-time values
        binding.includeLossOfCharge.locTvScreenOffPercentageDropped.text =
            String.format("%.1f", screenOffPercentDropped)
        binding.includeLossOfCharge.locTvScreenOffMahConsumed.text =
            String.format("%.1f", screenOffMahConsumed)

        val totalSessionConsumption = screenOnPercentDropped + screenOffPercentDropped

        Log.d(TAG, "REAL_TIME_CONSUMPTION: Updated UI with real-time session totals - " +
              "Screen ON: ${String.format("%.1f", screenOnPercentDropped)}% (${String.format("%.1f", screenOnMahConsumed)}mAh), " +
              "Screen OFF: ${String.format("%.1f", screenOffPercentDropped)}% (${String.format("%.1f", screenOffMahConsumed)}mAh), " +
              "Total Session: ${String.format("%.1f", totalSessionConsumption)}%")

        // Update screen time displays from UI state with validation
        val totalScreenTime = state.screenOnTimeUI + state.screenOffTimeUI
        val sessionDuration = session.durationMillis

        // Validate mathematical constraint: screen time sum ≤ session duration
        if (totalScreenTime > sessionDuration) {
            Log.w(TAG, "MATH_CONSTRAINT_VIOLATION: Screen time sum (${totalScreenTime/1000}s) exceeds session duration (${sessionDuration/1000}s). " +
                  "ON: ${state.screenOnTimeUI/1000}s, OFF: ${state.screenOffTimeUI/1000}s")
        }

        val screenOnTimeFormatted = timeConverter.formatMillisToHoursMinutesSeconds(state.screenOnTimeUI)
        val screenOffTimeFormatted = timeConverter.formatMillisToHoursMinutesSeconds(state.screenOffTimeUI)

        binding.includeLossOfCharge.locTvScreenOnTime.text = screenOnTimeFormatted
        binding.includeLossOfCharge.locTvScreenOffTime.text = screenOffTimeFormatted

        // Log every 30 seconds to reduce noise
        if ((state.screenOnTimeUI / 1000) % 30 == 0L || (state.screenOffTimeUI / 1000) % 30 == 0L) {
            Log.d(TAG, "UI_TIME_DISPLAY: Updated screen time UI display - " +
                "ON: $screenOnTimeFormatted (${state.screenOnTimeUI/1000}s), " +
                "OFF: $screenOffTimeFormatted (${state.screenOffTimeUI/1000}s), " +
                "Total: ${totalScreenTime/1000}s, Session: ${sessionDuration/1000}s")
        }

        Log.d(TAG, "TC2.2: Updated loss of charge section - " +
             "Screen ON: ${String.format("%.1f", screenOnPercentDropped)}%, " +
             "${String.format("%.1f", session.screenOnMahConsumed)} mAh, " +
             "${state.screenOnTimeUI/1000}s, " +
             "Screen OFF: ${String.format("%.1f", screenOffPercentDropped)}%, " +
             "${String.format("%.1f", session.screenOffMahConsumed)} mAh, " +
             "${state.screenOffTimeUI/1000}s")
    }

    /**
     * Updates the current session details section
     */
    fun updateCurrentSessionDetails(state: DischargeUiState) {
        val session = state.currentSession
        if (session == null || state.isLoadingInitial || state.isCharging) {
            // Hide section if no active session
            binding.includeCurrentSessionDetails.root.visibility = View.GONE
            return
        }

        // If the session block was previously hidden, this is a good sign that our caching is working
        if (binding.includeCurrentSessionDetails.root.visibility != View.VISIBLE) {
            Log.d(TAG, "TC2.1: Session block becoming visible - validation that session data is displayed after being restored/created")
        }

        binding.includeCurrentSessionDetails.root.visibility = View.VISIBLE

        // Total time and current rate
        val durationFormatted = timeConverter.formatMillisToHoursMinutes(session.durationMillis)
        binding.includeCurrentSessionDetails.csdTvTotalTimeValue.text = durationFormatted
        binding.includeCurrentSessionDetails.csdTvSessionStartTime.text =
            timeConverter.formatTimestamp(session.startTimeEpochMillis)
        binding.includeCurrentSessionDetails.csdTvCurrentRateValue.text =
            String.format("%.1f", session.currentDischargeRate)

        // Average speed and total consumed
        binding.includeCurrentSessionDetails.csdTvAvgSpeedPercentValue.text =
            String.format("%.1f", session.avgPercentPerHour)
        binding.includeCurrentSessionDetails.csdTvAvgSpeedMahValue.text =
            String.format("%.1f", session.avgMixedDischargeRateMahPerHour)

        binding.includeCurrentSessionDetails.csdTvTotalConsumedPercentValue.text =
            session.percentageDropped.toString()
        binding.includeCurrentSessionDetails.csdTvTotalConsumedMahValue.text =
            String.format("%.1f", session.totalMahConsumed)

        // Screen off/on specific stats
        binding.includeCurrentSessionDetails.csdTvScreenOffPercentValue.text =
            String.format("%.1f", session.avgScreenOffDischargeRatePercentPerHour)
        binding.includeCurrentSessionDetails.csdTvScreenOffMahValue.text =
            String.format("%.1f", session.avgScreenOffDischargeRateMahPerHour)

        binding.includeCurrentSessionDetails.csdTvScreenOnPercentValue.text =
            String.format("%.1f", session.avgScreenOnDischargeRatePercentPerHour)
        binding.includeCurrentSessionDetails.csdTvScreenOnMahValue.text =
            String.format("%.1f", session.avgScreenOnDischargeRateMahPerHour)

        Log.d(TAG, "TC2.2: Displaying session data - isActive=${session.isActive}, " +
               "duration=${durationFormatted}, " +
               "battery: ${session.startPercentage}% → ${session.currentPercentage}% (${session.percentageDropped}% drop)")
    }

    /**
     * Sets the colored charging text with percentage highlighted
     */
    private fun setColoredChargingText(percent: Int, isCharging: Boolean) {
        if (isCharging) {
            // Charging string has format parameter
            val fullText = context.getString(R.string.charging, percent.toString())
            val percentStr = "$percent%"
            val start = fullText.indexOf(percentStr)

            if (start >= 0) {
                val end = start + percentStr.length
                val spannable = SpannableString(fullText)

                val greenColor = getThemeColor(context, R.attr.colorr)
                spannable.setSpan(
                    ForegroundColorSpan(greenColor),
                    start,
                    end,
                    android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = spannable
            } else {
                // If percentage not found in string, just set the text without formatting
                binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = fullText
                Log.w(TAG, "Could not find percentage text '$percentStr' in string '$fullText'")
            }
        } else {
            // Discharging string doesn't have format parameter
            val dischargingText = context.getString(R.string.discharging)
            // Add the percentage after the discharging text and append "is enough for"
            val fullText = "$dischargingText $percent% is enough for"

            val spannable = SpannableString(fullText)
            val start = fullText.indexOf("$percent%")

            if (start >= 0) {
                val end = start + "$percent%".length
                val greenColor = getThemeColor(context, R.attr.colorr)
                spannable.setSpan(
                    ForegroundColorSpan(greenColor),
                    start,
                    end,
                    android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }

            binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = spannable
        }
    }

    /**
     * Gets a color from the current theme
     */
    private fun getThemeColor(context: Context, attr: Int): Int {
        val typedValue = android.util.TypedValue()
        val theme = context.theme
        theme.resolveAttribute(attr, typedValue, true)
        return typedValue.data
    }
}
