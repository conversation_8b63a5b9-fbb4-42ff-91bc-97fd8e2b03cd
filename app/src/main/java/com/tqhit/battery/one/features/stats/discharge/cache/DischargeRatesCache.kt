package com.tqhit.battery.one.features.stats.discharge.cache

interface DischargeRatesCache {
    suspend fun getAverageScreenOnRateMah(): Double?
    suspend fun saveAverageScreenOnRateMah(rateMah: Double)

    suspend fun getAverageScreenOffRateMah(): Double?
    suspend fun saveAverageScreenOffRateMah(rateMah: Double)

    // Real-time consumption percentage tracking
    suspend fun getScreenOnConsumePercentage(): Double?
    suspend fun saveScreenOnConsumePercentage(percentage: Double)

    suspend fun getScreenOffConsumePercentage(): Double?
    suspend fun saveScreenOffConsumePercentage(percentage: Double)
}
