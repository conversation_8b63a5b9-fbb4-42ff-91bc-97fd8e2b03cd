package com.tqhit.battery.one.features.stats.discharge.presentation

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator
import com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter
import com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI state for the discharge screen
 */
data class DischargeUiState(
    val batteryPercentage: Int = 0,
    val isCharging: Boolean = false,
    val isLoadingInitial: Boolean = true,
    // Time estimation fields
    val screenOnTimeRemainingMs: Long = 0L,
    val screenOffTimeRemainingMs: Long = 0L,
    val mixedUsageTimeRemainingMs: Long = 0L,
    val screenOnTimeAt100PercentMs: Long = 0L,
    val screenOffTimeAt100PercentMs: Long = 0L,
    val mixedUsageTimeAt100PercentMs: Long = 0L,
    val areTimeEstimationsLoading: Boolean = true,
    // Phase 2: Current session data
    val currentSession: DischargeSessionData? = null,
    val isCurrentSessionLoading: Boolean = true,
    // UI Screen time tracking
    val screenOnTimeUI: Long = 0L,
    val screenOffTimeUI: Long = 0L,
    // Real-time consumption tracking
    val screenOnConsumePercentage: Double = 0.0,
    val screenOffConsumePercentage: Double = 0.0,
    // Battery capacity for app power consumption calculations
    val batteryCapacityMah: Int = 0
)

/**
 * ViewModel for the discharge screen
 */
@HiltViewModel
class DischargeViewModel @Inject constructor(
    private val batteryRepository: BatteryRepository,
    private val calculator: DischargeCalculator,
    private val dischargeSessionRepository: DischargeSessionRepository,
    private val timeConverter: TimeConverter,
    private val enhancedDischargeTimerServiceHelper: EnhancedDischargeTimerServiceHelper
) : ViewModel() {
    companion object {
        private const val TAG = "DischargeViewModel"
    }

    private val _uiState = MutableStateFlow(DischargeUiState())
    val uiState: StateFlow<DischargeUiState> = _uiState.asStateFlow()

    init {
        Log.d(TAG, "Initializing DischargeViewModel")

        // Force screen state check on initialization to ensure consistency
        viewModelScope.launch {
            // Give the repository time to initialize
            kotlinx.coroutines.delay(1000)
            Log.d(TAG, "Performing initial screen state verification")
            dischargeSessionRepository.forceCheckScreenState()
        }

        // Collect basic battery info updates
        viewModelScope.launch {
            batteryRepository.batteryStatusFlow.collect { status ->
                Log.d(TAG, "Received battery status update: $status")

                _uiState.update { currentState ->
                    currentState.copy(
                        batteryPercentage = status.percentage,
                        isCharging = status.isCharging,
                        isLoadingInitial = false,
                        batteryCapacityMah = batteryRepository.getEffectiveCapacityMah()
                    )
                }

                // Start the enhanced discharge timer service if we have an active session and not charging
                // This ensures the service is running whenever we have an active discharge session
                val currentSession = dischargeSessionRepository.currentSession.value
                if (currentSession != null && currentSession.isActive && !status.isCharging) {
                    if (!enhancedDischargeTimerServiceHelper.isServiceRunning()) {
                        Log.i(TAG, "VIEW_MODEL: Starting EnhancedDischargeTimerService due to active session. session.isActive=${currentSession.isActive}, status.isCharging=${status.isCharging}")
                        enhancedDischargeTimerServiceHelper.startService()
                    }
                } else if (status.isCharging) {
                    // Stop the service if charging
                    if (enhancedDischargeTimerServiceHelper.isServiceRunning()) {
                        Log.i(TAG, "VIEW_MODEL: Stopping EnhancedDischargeTimerService due to charging. status.isCharging=${status.isCharging}")
                        enhancedDischargeTimerServiceHelper.stopService()
                    }
                }
            }
        }
        
        // Separate launch for time estimations on Default dispatcher
        viewModelScope.launch(Dispatchers.Default) {
            Log.d(TAG, "Starting time estimation calculation flow")
            
            // Combine battery status and discharge rates to calculate time estimations
            combine(
                batteryRepository.batteryStatusFlow,
                batteryRepository.averageScreenOnDischargeRateMah,
                batteryRepository.averageScreenOffDischargeRateMah
            ) { status, screenOnRate, screenOffRate ->
                Log.d(TAG, "Calculating time estimations with rates: screenOn=$screenOnRate mA, screenOff=$screenOffRate mA")
                
                // Only calculate if we're discharging
                if (status.isCharging) {
                    Log.d(TAG, "Device is charging - skipping time estimations")
                    return@combine TimeEstimations()
                }
                
                val effectiveCapacityMah = batteryRepository.getEffectiveCapacityMah().toDouble()
                val currentCapacityMah = calculator.calculateCurrentCapacityMah(status.percentage, effectiveCapacityMah)
                Log.d(TAG, "Using battery capacity: $effectiveCapacityMah mAh, current capacity: $currentCapacityMah mAh (${status.percentage}%)")
                
                // Ensure rates are positive - use learned rates or fallback to defaults
                val positiveScreenOnRate = if (screenOnRate > 0) screenOnRate 
                    else DischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA
                val positiveScreenOffRate = if (screenOffRate > 0) screenOffRate 
                    else DischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA
                
                // Calculate mixed usage rate based on weighted average of learned rates
                val mixedRate = calculator.calculateMixedDischargeRate(positiveScreenOnRate, positiveScreenOffRate)
                Log.d(TAG, "Using discharge rates - screenOn: $positiveScreenOnRate mA, screenOff: $positiveScreenOffRate mA, mixed: $mixedRate mA")
                
                // Calculate time remaining for current charge using learned rates
                val screenOnTimeRemainingMs = calculator.estimateTimeRemainingMillis(currentCapacityMah, positiveScreenOnRate)
                val screenOffTimeRemainingMs = calculator.estimateTimeRemainingMillis(currentCapacityMah, positiveScreenOffRate)
                val mixedTimeRemainingMs = calculator.estimateTimeRemainingMillis(currentCapacityMah, mixedRate)
                
                // Calculate time for full battery (100%)
                val screenOnTimeAt100PercentMs = calculator.estimateTimeRemainingMillis(effectiveCapacityMah, positiveScreenOnRate)
                val screenOffTimeAt100PercentMs = calculator.estimateTimeRemainingMillis(effectiveCapacityMah, positiveScreenOffRate)
                val mixedTimeAt100PercentMs = calculator.estimateTimeRemainingMillis(effectiveCapacityMah, mixedRate)
                
                Log.d(TAG, "Time remaining calculations: " +
                    "screenOn=${timeConverter.formatMillisToHoursMinutes(screenOnTimeRemainingMs)}, " +
                    "screenOff=${timeConverter.formatMillisToHoursMinutes(screenOffTimeRemainingMs)}, " +
                    "mixed=${timeConverter.formatMillisToHoursMinutes(mixedTimeRemainingMs)}"
                )
                
                TimeEstimations(
                    screenOnTimeRemainingMs = screenOnTimeRemainingMs,
                    screenOffTimeRemainingMs = screenOffTimeRemainingMs,
                    mixedUsageTimeRemainingMs = mixedTimeRemainingMs,
                    screenOnTimeAt100PercentMs = screenOnTimeAt100PercentMs,
                    screenOffTimeAt100PercentMs = screenOffTimeAt100PercentMs,
                    mixedUsageTimeAt100PercentMs = mixedTimeAt100PercentMs
                )
            }.collect { times ->
                Log.d(TAG, "Updating UI with new time estimations: " + 
                    "screenOn=${timeConverter.formatMillisToHoursMinutes(times.screenOnTimeRemainingMs)}, " +
                    "screenOff=${timeConverter.formatMillisToHoursMinutes(times.screenOffTimeRemainingMs)}, " +
                    "mixed=${timeConverter.formatMillisToHoursMinutes(times.mixedUsageTimeRemainingMs)}"
                )
                
                _uiState.update {
                    it.copy(
                        screenOnTimeRemainingMs = times.screenOnTimeRemainingMs,
                        screenOffTimeRemainingMs = times.screenOffTimeRemainingMs,
                        mixedUsageTimeRemainingMs = times.mixedUsageTimeRemainingMs,
                        screenOnTimeAt100PercentMs = times.screenOnTimeAt100PercentMs,
                        screenOffTimeAt100PercentMs = times.screenOffTimeAt100PercentMs,
                        mixedUsageTimeAt100PercentMs = times.mixedUsageTimeAt100PercentMs,
                        areTimeEstimationsLoading = false
                    )
                }
            }
        }
        
        // Collect current discharge session updates
        viewModelScope.launch {
            dischargeSessionRepository.currentSession.collect { session ->
                Log.d(TAG, "Current discharge session update: ${session?.isActive}, startTime=${session?.startTimeEpochMillis}, percentage=${session?.currentPercentage}")
                
                _uiState.update { currentState ->
                    currentState.copy(
                        currentSession = session,
                        isCurrentSessionLoading = false
                    )
                }
                
                // Start or stop enhanced timer service based on session state
                if (session != null && session.isActive && !batteryRepository.batteryStatusFlow.first().isCharging) {
                    if (!enhancedDischargeTimerServiceHelper.isServiceRunning()) {
                        Log.i(TAG, "VIEW_MODEL: Starting EnhancedDischargeTimerService due to active session update. session.isActive=${session.isActive}, isCharging=${batteryRepository.batteryStatusFlow.first().isCharging}")
                        enhancedDischargeTimerServiceHelper.startService()
                    }
                } else if (session == null || !session.isActive) {
                    if (enhancedDischargeTimerServiceHelper.isServiceRunning()) {
                        Log.i(TAG, "VIEW_MODEL: Stopping EnhancedDischargeTimerService due to no active session. session.isActive=${session?.isActive}")
                        enhancedDischargeTimerServiceHelper.stopService()
                    }
                }
            }
        }
        
        // Collect screen time UI updates and real-time consumption
        viewModelScope.launch {
            combine(
                dischargeSessionRepository.screenOnTimeUI,
                dischargeSessionRepository.screenOffTimeUI,
                batteryRepository.screenOnConsumePercentage,
                batteryRepository.screenOffConsumePercentage
            ) { screenOnTimeUI, screenOffTimeUI, screenOnConsume, screenOffConsume ->
                ScreenTimeAndConsumption(screenOnTimeUI, screenOffTimeUI, screenOnConsume, screenOffConsume)
            }.collect { data ->
                // Log every 30 seconds to avoid too much noise
                if ((data.screenOnTimeUI / 1000) % 30 == 0L || (data.screenOffTimeUI / 1000) % 30 == 0L) {
                    Log.d(TAG, "UI_UPDATE: Times - ON: ${data.screenOnTimeUI/1000}s, OFF: ${data.screenOffTimeUI/1000}s, " +
                          "Consumption - ON: ${String.format("%.2f", data.screenOnConsume)}%, OFF: ${String.format("%.2f", data.screenOffConsume)}%")
                }

                _uiState.update { currentState ->
                    currentState.copy(
                        screenOnTimeUI = data.screenOnTimeUI,
                        screenOffTimeUI = data.screenOffTimeUI,
                        screenOnConsumePercentage = data.screenOnConsume,
                        screenOffConsumePercentage = data.screenOffConsume
                    )
                }
            }
        }
    }
    
    /**
     * Resets the current discharge session data
     */
    fun resetSessionData() {
        Log.d(TAG, "Resetting discharge session data")
        viewModelScope.launch {
            dischargeSessionRepository.clearActiveSessionData()
            
            // Stop the enhanced timer service
            if (enhancedDischargeTimerServiceHelper.isServiceRunning()) {
                Log.d(TAG, "Stopping EnhancedDischargeTimerService due to session reset")
                enhancedDischargeTimerServiceHelper.stopService()
            }
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        dischargeSessionRepository.cleanup()
        // We don't stop the service here as we want it to continue running
        // even when the user navigates away from the discharge fragment
    }
    
    /**
     * Helper data class to hold calculated time estimations
     */
    private data class TimeEstimations(
        val screenOnTimeRemainingMs: Long = 0L,
        val screenOffTimeRemainingMs: Long = 0L,
        val mixedUsageTimeRemainingMs: Long = 0L,
        val screenOnTimeAt100PercentMs: Long = 0L,
        val screenOffTimeAt100PercentMs: Long = 0L,
        val mixedUsageTimeAt100PercentMs: Long = 0L
    )

    /**
     * Helper data class to hold screen time and consumption data
     */
    private data class ScreenTimeAndConsumption(
        val screenOnTimeUI: Long,
        val screenOffTimeUI: Long,
        val screenOnConsume: Double,
        val screenOffConsume: Double
    )
}
