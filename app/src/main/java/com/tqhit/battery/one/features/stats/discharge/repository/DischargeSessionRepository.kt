package com.tqhit.battery.one.features.stats.discharge.repository

import android.content.Context
import android.os.PowerManager
import android.util.Log
import com.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import com.tqhit.battery.one.features.stats.discharge.data.ScreenStateChangeEvent
import com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver
import com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator
import com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator
import com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator
import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache
import com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker
import com.tqhit.battery.one.features.stats.discharge.domain.SessionManager
import com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator
import com.tqhit.battery.one.features.stats.discharge.domain.RealTimeConsumptionTracker
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository responsible for tracking and calculating battery discharge sessions
 */
@Singleton
class DischargeSessionRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val currentSessionCache: CurrentSessionCache,
    private val sessionManager: SessionManager,
    private val gapEstimationCalculator: GapEstimationCalculator,
    private val fullSessionReEstimator: FullSessionReEstimator,
    private val sessionMetricsCalculator: SessionMetricsCalculator,
    private val screenStateReceiver: ScreenStateReceiver,
    private val realTimeConsumptionTracker: RealTimeConsumptionTracker,
    private val dischargeRatesCache: DischargeRatesCache
) {
    companion object {
        private const val TAG = "DischargeSessionRepo" // Shortened for log readability
        private const val EFFECTIVE_CAPACITY_MAH = DischargeCalculator.DEFAULT_EFFECTIVE_CAPACITY_MAH
    }
    
    private val sessionScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
    
    private val _currentSession = MutableStateFlow<DischargeSessionData?>(null)
    val currentSession: StateFlow<DischargeSessionData?> = _currentSession.asStateFlow()
    
    // Screen state time tracker for UI updates
    private val screenStateTimeTracker = ScreenStateTimeTracker()

    // FIXED: Create fresh flows that always emit current values to prevent stale data
    val screenOnTimeUI = flow {
        while (true) {
            val currentValues = screenStateTimeTracker.incrementCurrentState()
            emit(currentValues.first)
            delay(1000) // Emit every second
        }
    }.distinctUntilChanged()

    val screenOffTimeUI = flow {
        while (true) {
            val currentValues = screenStateTimeTracker.incrementCurrentState()
            emit(currentValues.second)
            delay(1000) // Emit every second
        }
    }.distinctUntilChanged()
    
    private var lastProcessedStatus: CoreBatteryStatus? = null
    private var lastScreenState: Boolean = false
    private var lastScreenStateChangeTime: Long = System.currentTimeMillis()
    
    // Flag to track if we've just completed a full session re-estimation
    private var justCompletedReEstimation = false
    
    /**
     * Initializes the repository by loading any cached session and registering receivers
     */
    init {
        // Initialize the screen state
        try {
            lastScreenState = powerManager.isInteractive
            Log.d(TAG, "SCREEN_TRACKER: Initial screen state is ${if (lastScreenState) "ON" else "OFF"}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get initial screen state", e)
            // Default to screen on if there's an error
            lastScreenState = true
        }
        
        sessionScope.launch {
            loadCachedSession()
            
            // Register screen state receiver
            screenStateReceiver.register()
            
            // Collect screen state change events
            screenStateReceiver.screenStateFlow.collect { event ->
                handleScreenStateChangeEvent(event)
            }
        }
    }
    
    /**
     * Loads the cached session if available
     */
    private suspend fun loadCachedSession() {
        Log.d(TAG, "Checking for cached discharge session")
        val cachedSession = currentSessionCache.getCurrentSession()
        if (cachedSession != null) {
            if (cachedSession.isActive) {
                Log.d(TAG, "TC2.1: Restored active session from cache - started at ${cachedSession.startTimeEpochMillis}, " +
                       "battery: ${cachedSession.startPercentage}% → ${cachedSession.currentPercentage}%")
                _currentSession.value = cachedSession

                // Verify and initialize screen state properly
                val actualScreenState = powerManager.isInteractive
                Log.d(TAG, "SCREEN_INIT: Actual screen state during session restore: ${if (actualScreenState) "ON" else "OFF"}")

                // Initialize screen time tracker with session data and verified screen state
                screenStateTimeTracker.initialize(
                    cachedSession.screenOnTimeMillis,
                    cachedSession.screenOffTimeMillis,
                    actualScreenState
                )

                // Update internal tracking to match actual state
                lastScreenState = actualScreenState
                lastScreenStateChangeTime = System.currentTimeMillis()

                Log.d(TAG, "SCREEN_INIT: Initialized screen time tracker with session data - " +
                      "ON: ${cachedSession.screenOnTimeMillis/1000}s, OFF: ${cachedSession.screenOffTimeMillis/1000}s, " +
                      "Current state: ${if (actualScreenState) "ON" else "OFF"}")
            } else {
                Log.d(TAG, "Found inactive cached session - not restoring")
            }
        } else {
            Log.d(TAG, "No cached session found")
            // Initialize screen time tracker for new session
            val actualScreenState = powerManager.isInteractive
            screenStateTimeTracker.initialize(0L, 0L, actualScreenState)
            lastScreenState = actualScreenState
            lastScreenStateChangeTime = System.currentTimeMillis()
            Log.d(TAG, "SCREEN_INIT: No cached session - initialized tracker with current state: ${if (actualScreenState) "ON" else "OFF"}")
        }
    }
    
    /**
     * Processes the latest battery status and updates the session accordingly
     * This is the main entry point for battery status updates
     */
    suspend fun processBatteryStatus(status: CoreBatteryStatus) {
        withContext(Dispatchers.Default) {
            val isScreenOn = powerManager.isInteractive
            val now = System.currentTimeMillis()
            
            // Log detailed battery status info
            Log.d(TAG, "SESSION_REPO: processBatteryStatus - input.isCharging=${status.isCharging}, " +
                  "screenIsOn=${isScreenOn}, currentTime=${now}")
            
            // Handle screen state change through the PowerManager check
            // This is a fallback method that will be used alongside the broadcast receiver
            handleScreenStateChange(isScreenOn, now)
            
            val current = _currentSession.value
            val lastStatus = lastProcessedStatus
            
            Log.d(TAG, "SESSION_REPO: processBatteryStatus - currentSession.isActive=${current?.isActive}, " +
                  "currentSession.startPercent=${current?.startPercentage}")
            
            when {
                status.isCharging -> {
                    Log.d(TAG, "SESSION_REPO: Handling charging state.")
                    handleChargingState(current)
                }
                current == null || !current.isActive -> {
                    Log.d(TAG, "SESSION_REPO: Condition met to START new discharge session.")
                    startNewSession(status)
                }
                lastStatus != null -> {
                    // Log significant updates
                    logBatteryPercentageChange(lastStatus, status)
                    // Update existing session
                    updateSession(status, lastStatus, now, isScreenOn)
                    // Process real-time consumption tracking
                    realTimeConsumptionTracker.processBatteryStatusUpdate(status)
                }
                else -> {
                    // First update after app restart with an active session
                    calculateAndApplyGapEstimation(current, status)
                    // Process real-time consumption tracking for gap estimation case
                    realTimeConsumptionTracker.processBatteryStatusUpdate(status)
                }
            }
            
            lastProcessedStatus = status
        }
    }
    
    /**
     * Force check the current screen state to ensure our tracking is accurate
     * Called periodically from the service and ViewModel
     */
    fun forceCheckScreenState() {
        // First, check the actual screen state from PowerManager
        val actualScreenState = powerManager.isInteractive
        Log.d(TAG, "SCREEN_TRACKER: Force checking screen state: internal=${if (lastScreenState) "ON" else "OFF"}, actual=${if (actualScreenState) "ON" else "OFF"}")
        
        // If there's a mismatch, update our internal state
        if (actualScreenState != lastScreenState) {
            Log.d(TAG, "SCREEN_TRACKER: State mismatch detected during force check. Updating from ${if (lastScreenState) "ON" else "OFF"} to ${if (actualScreenState) "ON" else "OFF"}")
            
            // Update the screenStateTimeTracker directly to ensure UI is updated
            screenStateTimeTracker.forceSetScreenState(actualScreenState)
            lastScreenState = actualScreenState
            lastScreenStateChangeTime = System.currentTimeMillis()
            
            // Use a new ScreenStateChangeEvent to handle the state change properly in the repository
            val event = ScreenStateChangeEvent(isScreenOn = actualScreenState)
            sessionScope.launch {
                handleScreenStateChangeEvent(event)
            }
        }
        
        // Also let the receiver do its check
        screenStateReceiver.forceCheckScreenState()
    }
    
    /**
     * Handles screen state change events from the broadcast receiver
     */
    private suspend fun handleScreenStateChangeEvent(event: ScreenStateChangeEvent) {
        withContext(Dispatchers.Default) {
            Log.d(TAG, "SCREEN_TRACKER: Received screen state change event: ${if (event.isScreenOn) "ON" else "OFF"} at ${event.timestamp}")
            
            // Update the screen time tracker (for UI)
            screenStateTimeTracker.handleScreenStateChange(event.isScreenOn)
            
            // Update session screen time (for data)
            val timeInPreviousState = kotlin.math.abs(event.timestamp - lastScreenStateChangeTime)
            
            // Only update if state actually changed
            if (event.isScreenOn != lastScreenState && timeInPreviousState > 0) {
                Log.d(TAG, "SCREEN_TRACKER: Screen state changed from ${if (lastScreenState) "ON" else "OFF"} to ${if (event.isScreenOn) "ON" else "OFF"}, " +
                      "time in previous state: ${timeInPreviousState}ms (${timeInPreviousState/1000}s)")
                
                // Update session with time spent in previous state
                updateSessionScreenTime(timeInPreviousState, !event.isScreenOn)
                
                // Update tracking variables
                lastScreenState = event.isScreenOn
                lastScreenStateChangeTime = event.timestamp
            } else if (event.isScreenOn != lastScreenState) {
                // State changed but no time has passed - just update tracking variables
                Log.d(TAG, "SCREEN_TRACKER: Screen state changed with no time passed. " +
                      "Updating from ${if (lastScreenState) "ON" else "OFF"} to ${if (event.isScreenOn) "ON" else "OFF"}")
                
                lastScreenState = event.isScreenOn
                lastScreenStateChangeTime = event.timestamp
            } else {
                // No change in state - do a verification check to ensure our internal state is correct
                // This helps catch cases where we might have missed a state change
                val actualScreenState = powerManager.isInteractive
                if (actualScreenState != lastScreenState) {
                    Log.d(TAG, "SCREEN_TRACKER: State verification failed - " +
                          "internal=${if (lastScreenState) "ON" else "OFF"}, actual=${if (actualScreenState) "ON" else "OFF"}. Correcting.")
                    
                    // Force a state change with the correct state
                    val correctEvent = ScreenStateChangeEvent(isScreenOn = actualScreenState)
                    handleScreenStateChangeEvent(correctEvent)
                } else {
                    Log.d(TAG, "SCREEN_TRACKER: State didn't change. Current state: ${if (lastScreenState) "ON" else "OFF"}, " +
                          "Event state: ${if (event.isScreenOn) "ON" else "OFF"}")
                }
            }
        }
    }
    
    /**
     * Handles screen state changes and updates tracked time
     * This is a fallback method that will be used alongside the broadcast receiver
     */
    private suspend fun handleScreenStateChange(isScreenOn: Boolean, now: Long) {
        if (isScreenOn != lastScreenState) {
            val timeInPreviousState = kotlin.math.abs(now - lastScreenStateChangeTime)
            Log.d(TAG, "Screen state changed from ${if (lastScreenState) "ON" else "OFF"} to ${if (isScreenOn) "ON" else "OFF"}, " +
                   "time in previous state: ${timeInPreviousState}ms")
            
            // Update session with time spent in previous state
            updateSessionScreenTime(timeInPreviousState, !isScreenOn)
            
            // Update screen time tracker (for UI)
            screenStateTimeTracker.handleScreenStateChange(isScreenOn)
            
            // Update tracking variables
            lastScreenState = isScreenOn
            lastScreenStateChangeTime = now
        }
    }
    
    /**
     * Handles the charging state - ends session if active
     */
    private suspend fun handleChargingState(current: DischargeSessionData?) {
        if (current?.isActive == true) {
            Log.d(TAG, "Battery is charging, ending discharge session")
            endCurrentSessionAndCache()
        }
    }
    
    /**
     * Logs battery percentage changes
     */
    private fun logBatteryPercentageChange(lastStatus: CoreBatteryStatus, status: CoreBatteryStatus) {
        if (lastStatus.percentage != status.percentage) {
            Log.d(TAG, "TC2.2: Battery percentage changed: ${lastStatus.percentage}% → ${status.percentage}%")
        }
    }

    /**
     * Called when the app restarts with an active session.
     * Uses FullSessionReEstimator to re-estimate the entire session from start to current.
     */
    private suspend fun calculateAndApplyGapEstimation(
        cachedSession: DischargeSessionData,
        liveStatus: CoreBatteryStatus
    ) {
        // Use FullSessionReEstimator instead of GapEstimationCalculator
        Log.d(TAG, "PHASE 2 - App restart detected with active session. Re-estimating full session from start to current.")

        val updatedSession = fullSessionReEstimator.reEstimateFullSessionScreenTimes(
            cachedSessionAtRestart = cachedSession,
            liveStatusAtRestart = liveStatus,
            effectiveCapacityMah = EFFECTIVE_CAPACITY_MAH
        )

        if (updatedSession != null) {
            _currentSession.value = updatedSession
            currentSessionCache.saveCurrentSession(updatedSession)

            // Re-initialize screen time tracker with re-estimated session data and verified screen state
            val actualScreenState = powerManager.isInteractive
            Log.d(TAG, "PHASE 2 - Re-initializing screen time tracker after gap estimation")
            Log.d(TAG, "PHASE 2 - Re-estimated session data - ON: ${updatedSession.screenOnTimeMillis/1000}s, OFF: ${updatedSession.screenOffTimeMillis/1000}s")
            Log.d(TAG, "PHASE 2 - Current actual screen state: ${if (actualScreenState) "ON" else "OFF"}")

            screenStateTimeTracker.initialize(
                updatedSession.screenOnTimeMillis,
                updatedSession.screenOffTimeMillis,
                actualScreenState
            )

            // Apply simplified gap estimation approach
            screenStateTimeTracker.applyGapEstimationResults(
                updatedSession.screenOnTimeMillis,
                updatedSession.startTimeEpochMillis
            )

            // Update internal tracking to match actual state
            lastScreenState = actualScreenState
            lastScreenStateChangeTime = System.currentTimeMillis()

            // Log the update with seconds for precision
            Log.d(TAG, "PHASE 2 - Updated session after full re-estimation - " +
                  "ON: ${updatedSession.screenOnTimeMillis/1000}s, " +
                  "OFF: ${updatedSession.screenOffTimeMillis/1000}s, " +
                  "total: ${String.format("%.1f", updatedSession.totalMahConsumed)}mAh, " +
                  "Current screen: ${if (actualScreenState) "ON" else "OFF"}")

            // Set flag to indicate we've just completed a re-estimation
            justCompletedReEstimation = true
        }

        // Update last processed status to avoid recalculation
        lastProcessedStatus = liveStatus
    }

    /**
     * Updates an existing session with new battery status information
     */
    private suspend fun updateSession(
        currentStatus: CoreBatteryStatus,
        lastStatus: CoreBatteryStatus,
        now: Long,
        isScreenOn: Boolean
    ) {
        val current = _currentSession.value ?: return

        // Log detailed information if this is the first update after a re-estimation
        if (justCompletedReEstimation) {
            Log.d(TAG, "PHASE 3 - First session update after re-estimation: " +
                  "Current ON: ${current.screenOnTimeMillis/1000}s, " +
                  "OFF: ${current.screenOffTimeMillis/1000}s, " +
                  "total: ${String.format("%.1f", current.totalMahConsumed)}mAh")
            justCompletedReEstimation = false
        }

        val updatedSession = sessionManager.updateSession(
            current = current,
            currentStatus = currentStatus,
            lastStatus = lastStatus,
            now = now,
            isScreenOn = isScreenOn,
            effectiveCapacityMah = EFFECTIVE_CAPACITY_MAH
        )

        // Log details of what was added during this update to verify Phase 3 is working correctly
        val onTimeDiffSeconds = (updatedSession.screenOnTimeMillis - current.screenOnTimeMillis) / 1000
        val offTimeDiffSeconds = (updatedSession.screenOffTimeMillis - current.screenOffTimeMillis) / 1000
        val mahDiff = updatedSession.totalMahConsumed - current.totalMahConsumed

        if (onTimeDiffSeconds > 0 || offTimeDiffSeconds > 0 || mahDiff > 0.01) {
            Log.d(TAG, "PHASE 3 - Incremental update: " +
                  "Added ON: ${onTimeDiffSeconds}s, " +
                  "Added OFF: ${offTimeDiffSeconds}s, " +
                  "Added mAh: ${String.format("%.2f", mahDiff)}, " +
                  "Screen is ${if (isScreenOn) "ON" else "OFF"}")
        }

        _currentSession.value = updatedSession
        currentSessionCache.saveCurrentSession(updatedSession)
    }

    /**
     * Starts a new discharge session
     */
    private suspend fun startNewSession(status: CoreBatteryStatus) {
        Log.i(TAG, "SESSION_REPO: Starting new discharge session at ${System.currentTimeMillis()}, battery=${status.percentage}%")

        val newSession = sessionManager.createNewSession(status)

        _currentSession.value = newSession
        currentSessionCache.saveCurrentSession(newSession)

        // Make sure we have the latest screen state
        lastScreenState = powerManager.isInteractive
        // Use session start time for consistent timing
        lastScreenStateChangeTime = newSession.startTimeEpochMillis

        // Reset screen time tracker
        screenStateTimeTracker.reset()
        // Explicitly initialize the screen state tracker with the current screen state
        screenStateTimeTracker.initialize(0L, 0L, lastScreenState)

        // Initialize real-time consumption tracker for new session
        // Reset previous session data and initialize with cached learning rates
        realTimeConsumptionTracker.resetForNewSession()
        initializeRealTimeConsumptionTracker(status)

        Log.i(TAG, "SESSION_REPO: New session started. Reset lastScreenStateChangeTime=${lastScreenStateChangeTime}, " +
              "lastScreenState=${if(lastScreenState) "ON" else "OFF"}")
        Log.i(TAG, "SESSION_REPO: ScreenStateTimeTracker (domain) reset and initialized for screen ${if(lastScreenState) "ON" else "OFF"}")
        Log.i(TAG, "SESSION_REPO: RealTimeConsumptionTracker reset for new session")
    }

    /**
     * Updates the session's screen time based on a screen state change
     */
    private suspend fun updateSessionScreenTime(timeMs: Long, wasScreenOff: Boolean) {
        val current = _currentSession.value ?: return

        val updatedSession = sessionManager.updateSessionScreenTime(current, timeMs, wasScreenOff)
        if (updatedSession != null) {
            // Log detailed information about the screen time update to verify Phase 3
            val screenStateName = if (wasScreenOff) "OFF" else "ON"
            val addedTimeSeconds = timeMs / 1000
            val previousTimeSeconds = if (wasScreenOff)
                current.screenOffTimeMillis / 1000 else current.screenOnTimeMillis / 1000
            val newTimeSeconds = if (wasScreenOff)
                updatedSession.screenOffTimeMillis / 1000 else updatedSession.screenOnTimeMillis / 1000

            Log.d(TAG, "PHASE 3 - Screen state update: Added ${addedTimeSeconds}s to ${screenStateName} time, " +
                  "Previous: ${previousTimeSeconds}s → New: ${newTimeSeconds}s")

            _currentSession.value = updatedSession
            currentSessionCache.saveCurrentSession(updatedSession)
        }
    }

    /**
     * Ends and finalizes the current session
     */
    private suspend fun endCurrentSessionAndCache() {
        val current = _currentSession.value ?: return

        val finalizedSession = sessionManager.finalizeSession(current)
        if (finalizedSession != null) {
            _currentSession.value = finalizedSession
            currentSessionCache.saveCurrentSession(finalizedSession)
        }
    }

    /**
     * Clears the current active session and cache
     */
    suspend fun clearActiveSessionData() {
        val currentSession = _currentSession.value
        if (currentSession != null) {
            sessionMetricsCalculator.logDetailedSessionInfo(currentSession)
        } else {
            Log.d(TAG, "Clearing discharge session (no active session found)")
        }

        _currentSession.value = null
        currentSessionCache.clearCurrentSession()

        // Reset screen time tracker
        screenStateTimeTracker.reset()

        Log.d(TAG, "Session reset complete")
    }

    // Track when we last applied constraint enforcement to prevent oscillation
    private var lastConstraintEnforcementTime = 0L
    private var constraintEnforcementCount = 0
    private var lastConstraintCountReset = System.currentTimeMillis()
    private val CONSTRAINT_ENFORCEMENT_COOLDOWN_MS = 5000L // 5 seconds cooldown
    private val MAX_CONSTRAINT_ENFORCEMENTS_PER_MINUTE = 3
    private val CONSTRAINT_COUNT_RESET_INTERVAL_MS = 60000L // 1 minute

    /**
     * Increments the current screen time state for UI updates (called every second)
     * Returns a pair of (screenOnTimeMs, screenOffTimeMs)
     */
    fun incrementScreenTimeForUI(): Pair<Long, Long> {
        // Double-check our screen state before incrementing
        val actualScreenState = powerManager.isInteractive
        if (actualScreenState != lastScreenState) {
            Log.d(TAG, "SCREEN_TRACKER_UI: Screen state mismatch detected during increment - " +
                  "internal=${if (lastScreenState) "ON" else "OFF"}, actual=${if (actualScreenState) "ON" else "OFF"}. Fixing.")

            // Force an update to the correct state
            screenStateTimeTracker.forceSetScreenState(actualScreenState)
            lastScreenState = actualScreenState
            lastScreenStateChangeTime = System.currentTimeMillis()
        }

        val (rawOnTime, rawOffTime) = screenStateTimeTracker.incrementCurrentState()

        // Apply smart constraint enforcement to prevent oscillation
        val currentSession = _currentSession.value
        val (constrainedOnTime, constrainedOffTime) = if (currentSession != null && currentSession.isActive) {
            applySmartConstraintEnforcement(rawOnTime, rawOffTime, currentSession)
        } else {
            Pair(rawOnTime, rawOffTime)
        }

        // Enhanced logging to debug UI display discrepancies
        if ((constrainedOnTime / 1000) % 10 == 0L || (constrainedOffTime / 1000) % 10 == 0L) {
            val wasConstrained = (constrainedOnTime != rawOnTime || constrainedOffTime != rawOffTime)
            if (wasConstrained) {
                Log.d(TAG, "SCREEN_TRACKER_UI: CONSTRAINED times - " +
                      "Raw: ON=${rawOnTime/1000}s, OFF=${rawOffTime/1000}s → " +
                      "Final: ON=${constrainedOnTime/1000}s, OFF=${constrainedOffTime/1000}s")
            } else {
                Log.d(TAG, "SCREEN_TRACKER_UI: Current UI times - ON: ${constrainedOnTime/1000}s, OFF: ${constrainedOffTime/1000}s")
            }
        }

        return Pair(constrainedOnTime, constrainedOffTime)
    }

    /**
     * FIXED: Enhanced constraint enforcement that only triggers for actual violations
     * Apply force updates only when screen time EXCEEDS session duration (not when it's less)
     * This fixes the UI display discrepancy where OFF time was being incorrectly scaled up
     */
    private fun applySmartConstraintEnforcement(
        rawOnTime: Long,
        rawOffTime: Long,
        session: DischargeSessionData
    ): Pair<Long, Long> {
        val currentTime = System.currentTimeMillis()
        val currentSessionDuration = currentTime - session.startTimeEpochMillis
        val totalScreenTime = rawOnTime + rawOffTime

        // Reset constraint enforcement count periodically
        if (currentTime - lastConstraintCountReset > CONSTRAINT_COUNT_RESET_INTERVAL_MS) {
            constraintEnforcementCount = 0
            lastConstraintCountReset = currentTime
            Log.d(TAG, "CONSTRAINT_RESET: Reset enforcement count after 1 minute")
        }

        // Calculate constraint violation severity (only positive violations matter)
        val violationMs = totalScreenTime - currentSessionDuration
        val violationPercent = if (currentSessionDuration > 0) {
            (violationMs.toDouble() / currentSessionDuration.toDouble()) * 100.0
        } else 0.0

        // Check current screen state for force update optimization
        val isScreenOn = powerManager.isInteractive

        // FIXED: Only enforce constraints when screen time EXCEEDS session duration
        // This prevents unwanted scaling when our simplified calculation is working correctly
        val shouldEnforceConstraint = violationMs > 60000L && // Screen time exceeds session by more than 60 seconds
                violationPercent > 1.0 && // More than 1% violation
                (currentTime - lastConstraintEnforcementTime) > CONSTRAINT_ENFORCEMENT_COOLDOWN_MS &&
                constraintEnforcementCount < MAX_CONSTRAINT_ENFORCEMENTS_PER_MINUTE &&
                !isScreenOn // Only apply force updates when screen is OFF (as per requirements)

        if (shouldEnforceConstraint) {
            // Apply gentle scaling to bring screen time back within session duration
            val targetDuration = currentSessionDuration - 1000L // Leave 1 second buffer
            val scaleFactor = targetDuration.toDouble() / totalScreenTime.toDouble()
            val adjustedOnTime = (rawOnTime * scaleFactor).toLong()
            val adjustedOffTime = (rawOffTime * scaleFactor).toLong()

            Log.w(TAG, "CONSTRAINT_ENFORCEMENT: Screen time EXCEEDS session duration - applying scaling. " +
                  "Violation: ${violationMs/1000}s (${String.format("%.1f", violationPercent)}%). " +
                  "Raw: ON=${rawOnTime/1000}s + OFF=${rawOffTime/1000}s = ${totalScreenTime/1000}s, " +
                  "Session: ${currentSessionDuration/1000}s. " +
                  "Scaled to: ON=${adjustedOnTime/1000}s + OFF=${adjustedOffTime/1000}s")

            // Update tracker with gentle scaling
            screenStateTimeTracker.forceSetTimes(adjustedOnTime, adjustedOffTime)

            // Track enforcement to prevent oscillation
            lastConstraintEnforcementTime = currentTime
            constraintEnforcementCount++

            return Pair(adjustedOnTime, adjustedOffTime)
        } else {
            // Log constraint enforcement status for debugging
            if (violationMs > 0) {
                val reason = when {
                    isScreenOn -> "screen is ON (avoiding disruption)"
                    violationMs <= 60000L -> "violation too small (${violationMs/1000}s)"
                    violationPercent <= 1.0 -> "violation percentage too low (${String.format("%.1f", violationPercent)}%)"
                    (currentTime - lastConstraintEnforcementTime) <= CONSTRAINT_ENFORCEMENT_COOLDOWN_MS -> "in cooldown period"
                    constraintEnforcementCount >= MAX_CONSTRAINT_ENFORCEMENTS_PER_MINUTE -> "max enforcements reached"
                    else -> "unknown"
                }
                Log.d(TAG, "CONSTRAINT_SKIP: Screen time exceeds session by ${violationMs/1000}s (${String.format("%.1f", violationPercent)}%) - " +
                      "not enforcing: $reason")
            } else if (violationMs < -60000L) {
                // Screen time is significantly less than session duration - this is normal with our simplified approach
                Log.v(TAG, "CONSTRAINT_OK: Screen time is ${kotlin.math.abs(violationMs)/1000}s less than session duration - " +
                      "this is expected with simplified calculation (OFF = Total - ON)")
            }

            // Still verify consistency periodically but don't force corrections for normal cases
            verifyScreenTimeConsistency(rawOnTime, rawOffTime, session)
            return Pair(rawOnTime, rawOffTime)
        }
    }

    /**
     * FIXED: Verifies UI screen time consistency with improved tolerance for simplified calculation
     * The simplified approach (OFF = Total - ON) may naturally differ from session data, which is expected
     */
    private fun verifyScreenTimeConsistency(uiOnTime: Long, uiOffTime: Long, session: DischargeSessionData) {
        val sessionOnTime = session.screenOnTimeMillis
        val sessionOffTime = session.screenOffTimeMillis
        val sessionDuration = session.durationMillis
        val uiTotalTime = uiOnTime + uiOffTime

        // Check the core mathematical constraint: Screen On + Screen Off should approximately equal session duration
        val totalTimeGap = kotlin.math.abs(uiTotalTime - sessionDuration)
        val maxAcceptableGap = 120000L // Increased to 2 minutes for simplified calculation tolerance

        if (totalTimeGap > maxAcceptableGap) {
            Log.e(TAG, "SCREEN_TIME_GAP_VIOLATION: Screen time sum deviates significantly from session duration! " +
                  "UI Total: ${uiTotalTime/1000}s (ON: ${uiOnTime/1000}s + OFF: ${uiOffTime/1000}s) vs " +
                  "Session Duration: ${sessionDuration/1000}s. Gap: ${totalTimeGap/1000}s > ${maxAcceptableGap/1000}s limit")

            // Only force correction for extreme violations
            correctScreenTimeGap(uiOnTime, uiOffTime, session)
        } else {
            Log.v(TAG, "SCREEN_TIME_GAP_OK: Total time gap is ${totalTimeGap/1000}s (within ${maxAcceptableGap/1000}s limit)")
        }

        // Check mathematical constraint: screen time sum should not significantly exceed session duration
        val excessTime = uiTotalTime - sessionDuration
        if (excessTime > maxAcceptableGap) {
            Log.e(TAG, "MATH_CONSTRAINT_VIOLATION: UI screen time sum significantly exceeds session duration! " +
                  "Excess: ${excessTime/1000}s (UI Total: ${uiTotalTime/1000}s > Session: ${sessionDuration/1000}s)")
        }

        // RELAXED: Individual component comparison with higher tolerance for simplified calculation
        val componentTolerance = 300000L // 5 minutes tolerance for individual components

        val onTimeDiff = kotlin.math.abs(uiOnTime - sessionOnTime)
        val offTimeDiff = kotlin.math.abs(uiOffTime - sessionOffTime)

        if (onTimeDiff > componentTolerance || offTimeDiff > componentTolerance) {
            Log.w(TAG, "SCREEN_TIME_COMPONENT_DIFF: UI times differ from session data - " +
                  "UI ON: ${uiOnTime/1000}s vs Session ON: ${sessionOnTime/1000}s (diff: ${onTimeDiff/1000}s), " +
                  "UI OFF: ${uiOffTime/1000}s vs Session OFF: ${sessionOffTime/1000}s (diff: ${offTimeDiff/1000}s). " +
                  "Note: This is expected with simplified calculation (OFF = Total - ON)")

            // Only re-sync for extreme differences (more than 10 minutes)
            if (onTimeDiff > 600000L || offTimeDiff > 600000L) {
                Log.w(TAG, "SCREEN_TIME_RESYNC: Extreme discrepancy detected (>10min), re-syncing UI tracker with session data")
                val actualScreenState = powerManager.isInteractive
                screenStateTimeTracker.initialize(sessionOnTime, sessionOffTime, actualScreenState)
            }
        } else {
            Log.v(TAG, "SCREEN_TIME_COMPONENTS_OK: UI component times are within acceptable range of session data")
        }
    }

    /**
     * Corrects screen time gap when it exceeds the 60-second tolerance
     * Implements simplified calculation method as per requirements
     */
    private fun correctScreenTimeGap(uiOnTime: Long, uiOffTime: Long, session: DischargeSessionData) {
        val sessionDuration = session.durationMillis
        val isScreenOn = powerManager.isInteractive

        Log.i(TAG, "SCREEN_TIME_CORRECTION: Applying gap correction. Current screen state: ${if(isScreenOn) "ON" else "OFF"}")

        if (!isScreenOn) {
            // During Screen OFF periods, use simplified calculation: Screen OFF time = Total session time - Screen ON time
            val correctedOffTime = sessionDuration - uiOnTime
            val correctedOffTimePositive = kotlin.math.max(0L, correctedOffTime)

            Log.i(TAG, "SCREEN_TIME_CORRECTION: Screen OFF correction - " +
                  "Original OFF: ${uiOffTime/1000}s, Corrected OFF: ${correctedOffTimePositive/1000}s, " +
                  "ON time unchanged: ${uiOnTime/1000}s, Total: ${sessionDuration/1000}s")

            // Apply the correction to the screen state tracker
            screenStateTimeTracker.forceSetScreenOffTime(correctedOffTimePositive)
        } else {
            // During Screen ON periods, be more conservative and just log the issue
            Log.w(TAG, "SCREEN_TIME_CORRECTION: Gap detected during Screen ON period. " +
                  "Will be corrected during next Screen OFF period.")
        }
    }

    /**
     * Applies screen time gap correction from the enhanced timer service
     * This method is called by the background validation process
     */
    fun applyScreenTimeGapCorrection(correctedOffTime: Long) {
        Log.i(TAG, "GAP_CORRECTION_SERVICE: Applying gap correction from enhanced timer service - " +
              "Setting Screen OFF time to ${correctedOffTime/1000}s")

        screenStateTimeTracker.forceSetScreenOffTime(correctedOffTime)

        Log.i(TAG, "GAP_CORRECTION_SERVICE: Gap correction applied successfully")
    }

    /**
     * Initializes the real-time consumption tracker with cached learning rates.
     * This method provides access to cached discharge rates for proper initialization.
     */
    private suspend fun initializeRealTimeConsumptionTracker(status: CoreBatteryStatus) {
        try {
            // Get cached learning rates
            val cachedScreenOnRate = dischargeRatesCache.getAverageScreenOnRateMah() ?: DischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA
            val cachedScreenOffRate = dischargeRatesCache.getAverageScreenOffRateMah() ?: DischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA

            Log.d(TAG, "REAL_TIME_INIT: Initializing consumption tracker with cached rates - " +
                  "Screen ON: ${cachedScreenOnRate}mA, Screen OFF: ${cachedScreenOffRate}mA")

            // Initialize the tracker with battery status and cached rates
            realTimeConsumptionTracker.initializeSession(status, cachedScreenOnRate, cachedScreenOffRate)

            Log.d(TAG, "REAL_TIME_INIT: Real-time consumption tracker initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing real-time consumption tracker", e)
            // Initialize with default values as fallback
            realTimeConsumptionTracker.initializeSession(
                status,
                DischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA,
                DischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA
            )
        }
    }

    /**
     * Gets current consumption summary for logging/debugging.
     */
    fun getConsumptionSummary(): String {
        return realTimeConsumptionTracker.getConsumptionSummary()
    }

    /**
     * Cleans up resources when the repository is no longer needed
     */
    fun cleanup() {
        screenStateReceiver.unregister()
    }
}
