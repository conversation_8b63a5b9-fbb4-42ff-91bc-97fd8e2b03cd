package com.tqhit.battery.one.features.stats.discharge.domain

import android.content.Context
import android.os.PowerManager
import android.util.Log
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Real-time battery consumption tracker that directly measures battery drain
 * during each screen state rather than using time-based estimates.
 * 
 * Implementation follows the stats module architecture pattern and integrates
 * with CoreBatteryStatsService for consistent battery monitoring.
 */
@Singleton
class RealTimeConsumptionTracker @Inject constructor(
    @ApplicationContext private val context: Context,
    private val dischargeRatesCache: DischargeRatesCache
) {
    companion object {
        private const val TAG = "RealTimeConsumptionTracker"
        private const val MIN_BATTERY_CHANGE_THRESHOLD = 0.1 // Minimum battery change to track (0.1%)
    }

    private val trackerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val powerManager by lazy { context.getSystemService(Context.POWER_SERVICE) as PowerManager }

    // Real-time consumption state
    private val _screenOnConsumePercentage = MutableStateFlow(0.0)
    val screenOnConsumePercentage: StateFlow<Double> = _screenOnConsumePercentage.asStateFlow()

    private val _screenOffConsumePercentage = MutableStateFlow(0.0)
    val screenOffConsumePercentage: StateFlow<Double> = _screenOffConsumePercentage.asStateFlow()

    // Tracking state variables
    private var batteryPercentageAtScreenOn: Double = 0.0
    private var batteryPercentageAtScreenOff: Double = 0.0
    private var lastKnownBatteryPercentage: Double = 0.0
    private var lastKnownScreenState: Boolean = false
    private var isInitialized: Boolean = false

    init {
        Log.d(TAG, "Initializing RealTimeConsumptionTracker")
        loadCachedConsumptionValues()
    }

    /**
     * Loads cached consumption values on app restart/initialization
     */
    private fun loadCachedConsumptionValues() {
        trackerScope.launch {
            try {
                val cachedScreenOnConsumption = dischargeRatesCache.getScreenOnConsumePercentage() ?: 0.0
                val cachedScreenOffConsumption = dischargeRatesCache.getScreenOffConsumePercentage() ?: 0.0

                _screenOnConsumePercentage.value = cachedScreenOnConsumption
                _screenOffConsumePercentage.value = cachedScreenOffConsumption

                Log.d(TAG, "Loaded cached consumption values - " +
                      "Screen ON: ${cachedScreenOnConsumption}%, " +
                      "Screen OFF: ${cachedScreenOffConsumption}%")
            } catch (e: Exception) {
                Log.e(TAG, "Error loading cached consumption values", e)
            }
        }
    }

    /**
     * Initializes the tracker with initial battery status and screen state.
     * Should be called when starting a new discharge session.
     */
    fun initializeSession(initialBatteryStatus: CoreBatteryStatus, cachedScreenOnRate: Double, cachedScreenOffRate: Double) {
        val currentScreenState = powerManager.isInteractive
        val currentBatteryPercentage = initialBatteryStatus.percentage.toDouble()

        Log.i(TAG, "INIT_SESSION: Initializing real-time consumption tracking - " +
              "Battery: ${currentBatteryPercentage}%, Screen: ${if(currentScreenState) "ON" else "OFF"}")

        // Calculate initial consumption values using cached learning rates
        // This provides continuity from previous sessions
        val initialScreenOnConsumption = calculateInitialConsumption(cachedScreenOnRate, "Screen ON")
        val initialScreenOffConsumption = calculateInitialConsumption(cachedScreenOffRate, "Screen OFF")

        // Set initial values
        _screenOnConsumePercentage.value = initialScreenOnConsumption
        _screenOffConsumePercentage.value = initialScreenOffConsumption

        // Set baseline battery percentages for tracking
        if (currentScreenState) {
            batteryPercentageAtScreenOn = currentBatteryPercentage
            batteryPercentageAtScreenOff = currentBatteryPercentage // Initialize both
        } else {
            batteryPercentageAtScreenOff = currentBatteryPercentage
            batteryPercentageAtScreenOn = currentBatteryPercentage // Initialize both
        }

        lastKnownBatteryPercentage = currentBatteryPercentage
        lastKnownScreenState = currentScreenState
        isInitialized = true

        // Save initial values to cache
        saveConsumptionToCache()

        Log.i(TAG, "INIT_SESSION: Initialized with Screen ON: ${initialScreenOnConsumption}%, " +
              "Screen OFF: ${initialScreenOffConsumption}%, " +
              "Baseline battery: ${currentBatteryPercentage}%")
    }

    /**
     * Calculates initial consumption based on cached learning rates and estimated session time.
     * This provides a reasonable starting point for real-time tracking.
     */
    private fun calculateInitialConsumption(cachedRate: Double, stateDescription: String): Double {
        // Use a conservative estimate based on cached rates
        // This will be replaced by real-time measurements as the session progresses
        val estimatedInitialConsumption = if (cachedRate > 0) {
            // Estimate based on typical session duration (e.g., 1 hour) and cached rate
            val estimatedHours = 1.0 // Conservative 1-hour estimate
            val estimatedMah = cachedRate * estimatedHours
            // Convert to percentage (assuming typical 3000mAh battery)
            (estimatedMah / 3000.0) * 100.0
        } else {
            0.0
        }

        Log.d(TAG, "INIT_CALC: $stateDescription initial consumption estimate: ${estimatedInitialConsumption}% " +
              "(based on cached rate: ${cachedRate}mA)")

        return estimatedInitialConsumption
    }

    /**
     * Processes battery status updates and tracks real-time consumption.
     * This is the main entry point for battery percentage changes.
     */
    fun processBatteryStatusUpdate(newStatus: CoreBatteryStatus) {
        if (!isInitialized) {
            Log.w(TAG, "PROCESS_UPDATE: Tracker not initialized, skipping update")
            return
        }

        val currentBatteryPercentage = newStatus.percentage.toDouble()
        val currentScreenState = powerManager.isInteractive

        // Check for significant battery percentage change
        val batteryChange = lastKnownBatteryPercentage - currentBatteryPercentage
        if (batteryChange < MIN_BATTERY_CHANGE_THRESHOLD) {
            // No significant battery change, just update screen state if needed
            handleScreenStateChange(currentScreenState, currentBatteryPercentage)
            return
        }

        Log.d(TAG, "PROCESS_UPDATE: Battery change detected - " +
              "From: ${lastKnownBatteryPercentage}% to ${currentBatteryPercentage}% " +
              "(Change: -${batteryChange}%), Screen: ${if(currentScreenState) "ON" else "OFF"}")

        // Track consumption based on current screen state
        trackConsumptionByScreenState(currentScreenState, currentBatteryPercentage, batteryChange)

        // Update tracking variables
        lastKnownBatteryPercentage = currentBatteryPercentage
        lastKnownScreenState = currentScreenState

        // Save updated values to cache
        saveConsumptionToCache()
    }

    /**
     * Handles screen state changes and updates baseline battery percentages.
     */
    private fun handleScreenStateChange(currentScreenState: Boolean, currentBatteryPercentage: Double) {
        if (lastKnownScreenState != currentScreenState) {
            Log.i(TAG, "SCREEN_STATE_CHANGE: ${if(lastKnownScreenState) "ON" else "OFF"} → " +
                  "${if(currentScreenState) "ON" else "OFF"} at ${currentBatteryPercentage}%")

            // Update baseline battery percentage for the new screen state
            if (currentScreenState) {
                // Screen turned ON
                batteryPercentageAtScreenOn = currentBatteryPercentage
            } else {
                // Screen turned OFF
                batteryPercentageAtScreenOff = currentBatteryPercentage
            }

            lastKnownScreenState = currentScreenState
        }
    }

    /**
     * Tracks actual battery consumption based on current screen state.
     */
    private fun trackConsumptionByScreenState(isScreenOn: Boolean, currentBatteryPercentage: Double, batteryChange: Double) {
        if (isScreenOn) {
            // Screen is ON - attribute consumption to screen ON
            val actualConsumedPercentage = batteryPercentageAtScreenOn - currentBatteryPercentage
            if (actualConsumedPercentage > 0) {
                val newScreenOnConsumption = _screenOnConsumePercentage.value + actualConsumedPercentage
                _screenOnConsumePercentage.value = newScreenOnConsumption

                Log.d(TAG, "TRACK_SCREEN_ON: Consumed ${actualConsumedPercentage}% while screen ON. " +
                      "Total screen ON consumption: ${newScreenOnConsumption}%")
            }
            // Reset baseline for next measurement
            batteryPercentageAtScreenOn = currentBatteryPercentage
        } else {
            // Screen is OFF - attribute consumption to screen OFF
            val actualConsumedPercentage = batteryPercentageAtScreenOff - currentBatteryPercentage
            if (actualConsumedPercentage > 0) {
                val newScreenOffConsumption = _screenOffConsumePercentage.value + actualConsumedPercentage
                _screenOffConsumePercentage.value = newScreenOffConsumption

                Log.d(TAG, "TRACK_SCREEN_OFF: Consumed ${actualConsumedPercentage}% while screen OFF. " +
                      "Total screen OFF consumption: ${newScreenOffConsumption}%")
            }
            // Reset baseline for next measurement
            batteryPercentageAtScreenOff = currentBatteryPercentage
        }
    }

    /**
     * Saves current consumption values to cache for persistence across app restarts.
     */
    private fun saveConsumptionToCache() {
        trackerScope.launch {
            try {
                dischargeRatesCache.saveScreenOnConsumePercentage(_screenOnConsumePercentage.value)
                dischargeRatesCache.saveScreenOffConsumePercentage(_screenOffConsumePercentage.value)

                Log.v(TAG, "CACHE_SAVE: Saved consumption to cache - " +
                      "Screen ON: ${_screenOnConsumePercentage.value}%, " +
                      "Screen OFF: ${_screenOffConsumePercentage.value}%")
            } catch (e: Exception) {
                Log.e(TAG, "Error saving consumption to cache", e)
            }
        }
    }

    /**
     * Resets consumption tracking for a new discharge session.
     */
    fun resetForNewSession() {
        Log.i(TAG, "RESET_SESSION: Resetting consumption tracking for new session")

        _screenOnConsumePercentage.value = 0.0
        _screenOffConsumePercentage.value = 0.0
        batteryPercentageAtScreenOn = 0.0
        batteryPercentageAtScreenOff = 0.0
        lastKnownBatteryPercentage = 0.0
        isInitialized = false

        // Clear cached values
        trackerScope.launch {
            try {
                dischargeRatesCache.saveScreenOnConsumePercentage(0.0)
                dischargeRatesCache.saveScreenOffConsumePercentage(0.0)
                Log.d(TAG, "RESET_SESSION: Cleared cached consumption values")
            } catch (e: Exception) {
                Log.e(TAG, "Error clearing cached consumption values", e)
            }
        }
    }

    /**
     * Gets current consumption summary for logging/debugging.
     */
    fun getConsumptionSummary(): String {
        val totalConsumption = _screenOnConsumePercentage.value + _screenOffConsumePercentage.value
        return "Session Totals - Screen ON: ${String.format("%.2f", _screenOnConsumePercentage.value)}%, " +
               "Screen OFF: ${String.format("%.2f", _screenOffConsumePercentage.value)}%, " +
               "Total Session: ${String.format("%.2f", totalConsumption)}%"
    }

    /**
     * Gets detailed consumption breakdown for UI display.
     */
    fun getDetailedConsumptionInfo(): ConsumptionInfo {
        val totalConsumption = _screenOnConsumePercentage.value + _screenOffConsumePercentage.value
        return ConsumptionInfo(
            screenOnPercent = _screenOnConsumePercentage.value,
            screenOffPercent = _screenOffConsumePercentage.value,
            totalSessionPercent = totalConsumption,
            isInitialized = isInitialized,
            lastKnownBatteryPercent = lastKnownBatteryPercentage
        )
    }

    /**
     * Data class to hold detailed consumption information.
     */
    data class ConsumptionInfo(
        val screenOnPercent: Double,
        val screenOffPercent: Double,
        val totalSessionPercent: Double,
        val isInitialized: Boolean,
        val lastKnownBatteryPercent: Double
    )
}
