#!/bin/bash

# Real-time Battery Consumption Tracking Test Script
# Bundle ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

echo "=== Real-time Battery Consumption Tracking Test ==="
echo "Bundle ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
echo ""

# Function to check if ADB is available
check_adb() {
    if ! command -v adb &> /dev/null; then
        echo "❌ ADB not found. Please install Android SDK platform-tools."
        exit 1
    fi
    echo "✅ ADB found"
}

# Function to check device connection
check_device() {
    local devices=$(adb devices | grep -v "List of devices" | grep -v "^$" | wc -l)
    if [ $devices -eq 0 ]; then
        echo "❌ No Android devices connected. Please connect a device or start an emulator."
        exit 1
    fi
    echo "✅ Android device connected"
}

# Function to build the app
build_app() {
    echo ""
    echo "🔨 Building the application..."
    if ./gradlew assembleDebug; then
        echo "✅ Build successful"
    else
        echo "❌ Build failed"
        exit 1
    fi
}

# Function to install the app
install_app() {
    echo ""
    echo "📱 Installing the application..."
    local apk_path="app/build/outputs/apk/debug/app-debug.apk"
    
    if [ ! -f "$apk_path" ]; then
        echo "❌ APK not found at $apk_path"
        exit 1
    fi
    
    if adb install -r "$apk_path"; then
        echo "✅ Installation successful"
    else
        echo "❌ Installation failed"
        exit 1
    fi
}

# Function to start the app
start_app() {
    echo ""
    echo "🚀 Starting the application..."
    adb shell am start -n "com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.MainActivity"
    echo "✅ Application started"
}

# Function to monitor logs
monitor_logs() {
    echo ""
    echo "📊 Monitoring real-time consumption tracking logs..."
    echo "Press Ctrl+C to stop monitoring"
    echo ""
    
    # Filter logs for real-time consumption tracking
    adb logcat -c  # Clear existing logs
    adb logcat | grep -E "(RealTimeConsumptionTracker|REAL_TIME_|CONSUMPTION|DischargeSessionRepo|BatteryRepository)" --line-buffered
}

# Function to test battery state changes
test_battery_states() {
    echo ""
    echo "🔋 Testing battery state changes..."
    echo ""
    
    echo "📋 Test Instructions:"
    echo "1. Ensure device is unplugged (discharging)"
    echo "2. Turn screen ON/OFF multiple times"
    echo "3. Watch for real-time consumption updates in logs"
    echo "4. Plug/unplug charger to test session transitions"
    echo ""
    
    echo "🔍 Key log patterns to watch for:"
    echo "- INIT_SESSION: Session initialization"
    echo "- TRACK_SCREEN_ON/OFF: Real-time consumption tracking"
    echo "- CACHE_SAVE: Persistence of consumption values"
    echo "- REAL_TIME_CONSUMPTION: UI updates"
    echo ""
}

# Function to check current battery status
check_battery_status() {
    echo ""
    echo "🔋 Current Battery Status:"
    local battery_level=$(adb shell dumpsys battery | grep level | cut -d: -f2 | tr -d ' ')
    local battery_status=$(adb shell dumpsys battery | grep status | cut -d: -f2 | tr -d ' ')
    local screen_state=$(adb shell dumpsys power | grep "Display Power" | head -1)
    
    echo "Battery Level: ${battery_level}%"
    echo "Battery Status: $battery_status (2=Charging, 3=Discharging, 4=Not Charging)"
    echo "Screen State: $screen_state"
    echo ""
}

# Function to simulate screen state changes
simulate_screen_changes() {
    echo ""
    echo "📱 Simulating screen state changes for testing..."
    echo "This will turn the screen ON/OFF 5 times with 10-second intervals"
    echo ""
    
    for i in {1..5}; do
        echo "Cycle $i/5:"
        echo "  Turning screen OFF..."
        adb shell input keyevent KEYCODE_POWER
        sleep 5
        
        echo "  Turning screen ON..."
        adb shell input keyevent KEYCODE_POWER
        adb shell input keyevent KEYCODE_MENU  # Unlock if needed
        sleep 5
        echo ""
    done
    
    echo "✅ Screen state simulation completed"
}

# Function to extract consumption data
extract_consumption_data() {
    echo ""
    echo "📊 Extracting consumption data from logs..."
    echo ""
    
    # Get recent consumption tracking logs
    adb logcat -d | grep -E "REAL_TIME_CONSUMPTION|TRACK_SCREEN_" | tail -20
    
    echo ""
    echo "📈 Summary of real-time consumption tracking:"
    adb logcat -d | grep "getConsumptionSummary" | tail -5
}

# Main execution
main() {
    echo "Starting real-time consumption tracking test..."
    
    check_adb
    check_device
    check_battery_status
    
    # Build and install
    build_app
    install_app
    start_app
    
    # Wait for app to initialize
    echo ""
    echo "⏳ Waiting 5 seconds for app initialization..."
    sleep 5
    
    test_battery_states
    
    echo ""
    echo "Choose test mode:"
    echo "1. Monitor logs only"
    echo "2. Simulate screen changes + monitor logs"
    echo "3. Extract consumption data"
    echo ""
    read -p "Enter choice (1-3): " choice
    
    case $choice in
        1)
            monitor_logs
            ;;
        2)
            # Start log monitoring in background
            monitor_logs &
            LOG_PID=$!
            
            # Wait a bit then simulate screen changes
            sleep 3
            simulate_screen_changes
            
            # Continue monitoring
            wait $LOG_PID
            ;;
        3)
            extract_consumption_data
            ;;
        *)
            echo "Invalid choice. Starting log monitoring..."
            monitor_logs
            ;;
    esac
}

# Handle script interruption
trap 'echo ""; echo "🛑 Test interrupted"; exit 0' INT

# Run main function
main
